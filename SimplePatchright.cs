using Microsoft.Playwright;

public class Simple<PERSON>atchrightExample
{
    public static async Task Main()
    {
        Console.WriteLine("Starting Simple Patchright C# Example...");

        try
        {
            // Install Playwright if needed
            Microsoft.Playwright.Program.Main(new[] { "install", "chromium" });

            // Create Playwright instance
            using var playwright = await Playwright.CreateAsync();
            
            // Launch browser with stealth args
            var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = false,
                Args = new[]
                {
                    "--disable-blink-features=AutomationControlled",
                    "--no-first-run",
                    "--password-store=basic",
                    "--use-mock-keychain"
                }
            });

            var page = await browser.NewPageAsync();

            // Add stealth script before navigation
            await page.AddInitScriptAsync(@"
                console.log('Stealth script loaded!');
                
                // Hide webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });

                // Spoof plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
                        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' }
                    ],
                    configurable: true
                });
            ");

            // Set up route interception for HTML injection
            await page.RouteAsync("**/*", async route =>
            {
                var request = route.Request;
                
                if (request.ResourceType == "document")
                {
                    try
                    {
                        var response = await route.FetchAsync();
                        var body = await response.TextAsync();
                        
                        // Simple script injection
                        if (body.Contains("<head>"))
                        {
                            var injectedScript = @"
                                <script>
                                    console.log('Script injected via route!');
                                    // Additional stealth code can go here
                                </script>";
                            
                            body = body.Replace("<head>", $"<head>{injectedScript}");
                        }

                        await route.FulfillAsync(new RouteFulfillOptions
                        {
                            Status = response.Status,
                            Headers = response.Headers,
                            Body = body
                        });
                    }
                    catch
                    {
                        // Fallback: continue without modification
                        await route.ContinueAsync();
                    }
                }
                else
                {
                    await route.ContinueAsync();
                }
            });

            // Navigate to test page
            Console.WriteLine("Navigating to example.com...");
            await page.GotoAsync("https://example.com");

            // Test stealth features
            Console.WriteLine("Testing stealth features...");
            
            var webdriverValue = await page.EvaluateAsync<object>("navigator.webdriver");
            Console.WriteLine($"navigator.webdriver: {webdriverValue ?? "undefined"}");

            var pluginsCount = await page.EvaluateAsync<int>("navigator.plugins.length");
            Console.WriteLine($"navigator.plugins.length: {pluginsCount}");

            // Take screenshot
            Console.WriteLine("Taking screenshot...");
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = "simple-patchright-test.png" });
            Console.WriteLine("Screenshot saved!");

            await page.CloseAsync();
            await browser.CloseAsync();
            
            Console.WriteLine("Example completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
