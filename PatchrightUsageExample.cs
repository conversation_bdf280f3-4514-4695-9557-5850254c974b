public class PatchrightUsageExample
{
    public static async Task Main()
    {
        Console.WriteLine("Starting Patchright C# Example...");

        try
        {
            // Create Patchright browser with configuration
            var config = new PatchrightConfig
            {
                EnableCspBypass = true,
                EnableScriptInjection = true,
                EnableNetworkInterception = true
            };

            await using var browser = await PatchrightFactory.CreateAsync(config);

                // Add init scripts (equivalent to <PERSON><PERSON>'s addInitScript)
                browser.AddInitScript(@"
                    console.log('Patchright init script loaded!');

                    // Hide webdriver property
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                        configurable: true
                    });

                    // Override permissions API
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({ state: Notification.permission }) :
                            originalQuery(parameters)
                    );

                    // Spoof plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [
                            { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
                            { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' }
                        ],
                        configurable: true
                    });
                ");

                // Add custom bindings
                browser.ExposeBinding("customFunction", async (name, args) =>
                {
                    Console.WriteLine($"Custom function called: {name} with args: {string.Join(", ", args)}");
                    return "success";
                });

                // Create a new page
                var page = await browser.NewPageAsync();
                Console.WriteLine("Page created successfully!");

                // Navigate to a simple test page first
                Console.WriteLine("Navigating to example.com...");
                await page.GotoAsync("https://example.com");

                // Take screenshot
                Console.WriteLine("Taking screenshot...");
                await page.ScreenshotAsync("patchright-test.png");
                Console.WriteLine("Screenshot saved as patchright-test.png");

                // Test the stealth features
                Console.WriteLine("Testing stealth features...");

                var webdriverValue = await page.EvaluateAsync<object>("navigator.webdriver");
                Console.WriteLine($"navigator.webdriver: {webdriverValue ?? "undefined"}");

                var pluginsCount = await page.EvaluateAsync<int>("navigator.plugins.length");
                Console.WriteLine($"navigator.plugins.length: {pluginsCount}");

                var userAgent = await page.EvaluateAsync<string>("navigator.userAgent");
                Console.WriteLine($"User Agent: {userAgent}");

                await page.CloseAsync();
                Console.WriteLine("Example completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }

    // Advanced example with custom stealth techniques
    public class AdvancedStealthExample
    {
        public static async Task StealthBrowsingExample()
        {
            var browser = await PatchrightFactory.CreateAsync();

            // Add comprehensive stealth scripts
            browser.AddInitScript(StealthScripts.WebDriverStealth);
            browser.AddInitScript(StealthScripts.ChromeRuntimeStealth);
            browser.AddInitScript(StealthScripts.PermissionsStealth);
            browser.AddInitScript(StealthScripts.PluginsStealth);

            var page = await browser.NewPageAsync();

            // Set realistic viewport
            await page.SetViewportSizeAsync(1366, 768);

            // Set user agent
            await page.SetUserAgentAsync(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            );

            // Navigate and test
            await page.GotoAsync("https://fingerprint.com/products/bot-detection/");
            
            // Wait and take screenshot
            await page.WaitForTimeoutAsync(5000);
            await page.ScreenshotAsync(new PageScreenshotOptions { Path = "stealth-test.png" });

            await browser.DisposeAsync();
        }
    }

    // Collection of stealth scripts
    public static class StealthScripts
    {
        public const string WebDriverStealth = @"
            // Remove webdriver property
            delete Object.getPrototypeOf(navigator).webdriver;
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });
        ";

        public const string ChromeRuntimeStealth = @"
            // Spoof chrome runtime
            if (!window.chrome) {
                window.chrome = {};
            }
            if (!window.chrome.runtime) {
                window.chrome.runtime = {
                    onConnect: { addListener: () => {} },
                    onMessage: { addListener: () => {} }
                };
            }
        ";

        public const string PermissionsStealth = @"
            // Override permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        ";

        public const string PluginsStealth = @"
            // Spoof plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
                    { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' },
                    { name: 'Native Client', filename: 'internal-nacl-plugin' }
                ],
                configurable: true
            });
        ";
    }

    // Example with error handling and logging
    public class ProductionExample
    {
        public static async Task ProductionUsageExample()
        {
            try
            {
                var config = new PatchrightConfig
                {
                    EnableCspBypass = true,
                    EnableScriptInjection = true,
                    EnableNetworkInterception = true
                };

                using var browser = await PatchrightFactory.CreateAsync(config);
                
                // Add comprehensive stealth
                browser.AddInitScript(StealthScripts.WebDriverStealth);
                browser.AddInitScript(StealthScripts.ChromeRuntimeStealth);

                // Add error handling binding
                browser.ExposeBinding("reportError", async (name, args) =>
                {
                    Console.WriteLine($"Page error: {args[0]}");
                    return null;
                });

                var page = await browser.NewPageAsync();

                // Add error monitoring
                await page.AddInitScriptAsync(@"
                    window.addEventListener('error', (e) => {
                        window.reportError(e.message);
                    });
                ");

                // Navigate with retry logic
                var maxRetries = 3;
                for (int i = 0; i < maxRetries; i++)
                {
                    try
                    {
                        await page.GotoAsync("https://example.com", new PageGotoOptions
                        {
                            WaitUntil = WaitUntilState.NetworkIdle,
                            Timeout = 30000
                        });
                        break;
                    }
                    catch (Exception ex) when (i < maxRetries - 1)
                    {
                        Console.WriteLine($"Navigation attempt {i + 1} failed: {ex.Message}");
                        await Task.Delay(1000);
                    }
                }

                // Perform actions
                await page.WaitForSelectorAsync("body");
                
                // Clean up injected scripts (remove evidence)
                await page.EvaluateAsync($@"
                    document.querySelectorAll('script.{((PatchrightPage)page)._scriptTag}').forEach(el => el.remove());
                ");

                await page.CloseAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
