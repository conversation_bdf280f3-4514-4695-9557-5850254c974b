# Patchright C# Setup Script

Write-Host "Setting up Patchright C# Example..." -ForegroundColor Green

# Restore NuGet packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
dotnet build

# Install Playwright browsers
Write-Host "Installing Playwright browsers..." -ForegroundColor Yellow
pwsh bin/Debug/net8.0/playwright.ps1 install chromium

Write-Host "Setup complete! You can now run the example with:" -ForegroundColor Green
Write-Host "dotnet run" -ForegroundColor Cyan
