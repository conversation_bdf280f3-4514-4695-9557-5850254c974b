using Microsoft.Playwright;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

namespace Patchright.CSharp
{
    public class PatchrightBrowser
    {
        private readonly IBrowser _browser;
        private readonly PatchrightConfig _config;
        private readonly List<string> _initScripts = new();
        private readonly Dictionary<string, Func<string, object[], Task<object>>> _bindings = new();

        public PatchrightBrowser(IBrowser browser, PatchrightConfig config)
        {
            _browser = browser;
            _config = config;
        }

        public async Task<IPatchrightPage> NewPageAsync()
        {
            var page = await _browser.NewPageAsync();
            var patchrightPage = new PatchrightPage(page, _config, _initScripts, _bindings);
            await patchrightPage.InitializeAsync();
            return patchrightPage;
        }

        public void AddInitScript(string script)
        {
            _initScripts.Add(script);
        }

        public void ExposeBinding(string name, Func<string, object[], Task<object>> callback)
        {
            _bindings[name] = callback;
        }
    }

    public interface IPatchrightPage : IPage
    {
        Task InitializeAsync();
    }

    public class PatchrightPage : IPatchrightPage
    {
        private readonly IPage _page;
        private readonly PatchrightConfig _config;
        private readonly List<string> _initScripts;
        private readonly Dictionary<string, Func<string, object[], Task<object>>> _bindings;
        private readonly string _scriptTag;

        public PatchrightPage(IPage page, PatchrightConfig config, 
            List<string> initScripts, Dictionary<string, Func<string, object[], Task<object>>> bindings)
        {
            _page = page;
            _config = config;
            _initScripts = initScripts;
            _bindings = bindings;
            _scriptTag = GenerateRandomId();
        }

        public async Task InitializeAsync()
        {
            // Set up network interception using Playwright routes
            await _page.RouteAsync("**/*", async route =>
            {
                var request = route.Request;
                
                // Handle special internal URLs for script injection
                if (IsInternalScriptUrl(request.Url))
                {
                    await HandleInternalScriptRequest(route);
                    return;
                }

                // Intercept HTML responses for script injection
                if (ShouldInterceptResponse(request))
                {
                    var response = await route.FetchAsync();
                    
                    if (IsHtmlResponse(response))
                    {
                        await InjectScriptsIntoHtml(route, response);
                    }
                    else
                    {
                        await route.FulfillAsync(response);
                    }
                }
                else
                {
                    await route.ContinueAsync();
                }
            });

            // Set up bindings
            foreach (var binding in _bindings)
            {
                await _page.ExposeFunctionAsync(binding.Key, binding.Value);
            }
        }

        private async Task InjectScriptsIntoHtml(IRoute route, IAPIResponse response)
        {
            try
            {
                var body = await response.TextAsync();
                var headers = response.Headers;
                
                // Generate or extract CSP nonce
                var nonce = ExtractOrGenerateNonce(headers);
                
                // Inject scripts into HTML
                var modifiedBody = InjectScripts(body, nonce);
                
                // Update CSP header if needed
                var modifiedHeaders = UpdateCspHeader(headers, nonce);

                await route.FulfillAsync(new RouteFulfillOptions
                {
                    Status = response.Status,
                    Headers = modifiedHeaders,
                    Body = modifiedBody
                });
            }
            catch (Exception ex)
            {
                // Fallback: continue without modification
                await route.FulfillAsync(response);
            }
        }

        private string InjectScripts(string html, string nonce)
        {
            if (!_initScripts.Any() && !_bindings.Any())
                return html;

            var scriptTags = new List<string>();

            // Add init scripts
            foreach (var script in _initScripts)
            {
                var scriptId = GenerateRandomId();
                var scriptTag = $@"<script class=""{_scriptTag}"" nonce=""{nonce}"" type=""text/javascript"">
                    document.getElementById(""{scriptId}"")?.remove();
                    {script}
                </script>";
                scriptTags.Add(scriptTag);
            }

            // Add binding scripts
            foreach (var binding in _bindings)
            {
                var bindingScript = GenerateBindingScript(binding.Key);
                var scriptTag = $@"<script class=""{_scriptTag}"" nonce=""{nonce}"" type=""text/javascript"">
                    {bindingScript}
                </script>";
                scriptTags.Add(scriptTag);
            }

            // Inject after <head> or at the beginning of <body>
            var injectionPoint = html.IndexOf("<head>", StringComparison.OrdinalIgnoreCase);
            if (injectionPoint >= 0)
            {
                injectionPoint = html.IndexOf(">", injectionPoint) + 1;
                return html.Insert(injectionPoint, string.Join("\n", scriptTags));
            }

            injectionPoint = html.IndexOf("<body>", StringComparison.OrdinalIgnoreCase);
            if (injectionPoint >= 0)
            {
                injectionPoint = html.IndexOf(">", injectionPoint) + 1;
                return html.Insert(injectionPoint, string.Join("\n", scriptTags));
            }

            // Fallback: prepend to HTML
            return string.Join("\n", scriptTags) + html;
        }

        private string ExtractOrGenerateNonce(Dictionary<string, string> headers)
        {
            // Try to extract existing nonce from CSP header
            if (headers.TryGetValue("content-security-policy", out var csp) ||
                headers.TryGetValue("content-security-policy-report-only", out csp))
            {
                var nonceMatch = Regex.Match(csp, @"script-src[^;]*'nonce-([^']+)'");
                if (nonceMatch.Success)
                {
                    return nonceMatch.Groups[1].Value;
                }
            }

            // Generate new nonce
            return GenerateRandomId();
        }

        private Dictionary<string, string> UpdateCspHeader(Dictionary<string, string> headers, string nonce)
        {
            var modifiedHeaders = new Dictionary<string, string>(headers);

            foreach (var cspHeader in new[] { "content-security-policy", "content-security-policy-report-only" })
            {
                if (modifiedHeaders.TryGetValue(cspHeader, out var cspValue))
                {
                    // Only modify if nonce isn't already present
                    if (!cspValue.Contains($"'nonce-{nonce}'"))
                    {
                        var scriptSrcRegex = new Regex(@"(script-src[^;]*)(;|$)");
                        var newCspValue = scriptSrcRegex.Replace(cspValue, $"$1 'nonce-{nonce}'$2");
                        modifiedHeaders[cspHeader] = newCspValue;
                    }
                }
            }

            return modifiedHeaders;
        }

        private bool IsInternalScriptUrl(string url)
        {
            return url.Contains("patchright-init-script-inject.internal");
        }

        private bool ShouldInterceptResponse(IRequest request)
        {
            return request.ResourceType == "document" || 
                   request.Url.Contains(".html") ||
                   request.Headers.ContainsKey("content-type") && 
                   request.Headers["content-type"].Contains("text/html");
        }

        private bool IsHtmlResponse(IAPIResponse response)
        {
            return response.Headers.TryGetValue("content-type", out var contentType) &&
                   contentType.Contains("text/html");
        }

        private async Task HandleInternalScriptRequest(IRoute route)
        {
            // Handle internal script injection requests
            await route.FulfillAsync(new RouteFulfillOptions
            {
                Status = 200,
                Headers = new Dictionary<string, string> { ["content-type"] = "text/javascript" },
                Body = "// Patchright internal script"
            });
        }

        private string GenerateBindingScript(string bindingName)
        {
            return $@"
                if (typeof window['{bindingName}'] === 'undefined') {{
                    window['{bindingName}'] = function(...args) {{
                        return window.playwright.{bindingName}(...args);
                    }};
                }}
            ";
        }

        private string GenerateRandomId()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[16];
            rng.GetBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        // Delegate all IPage methods to _page
        public IAccessibility Accessibility => _page.Accessibility;
        public IBrowserContext Context => _page.Context;
        public ICoverage Coverage => _page.Coverage;
        public IFrame MainFrame => _page.Frame;
        public IMouse Mouse => _page.Mouse;
        public IKeyboard Keyboard => _page.Keyboard;
        public ITouchscreen Touchscreen => _page.Touchscreen;
        public IRequest Request => _page.Request;
        
        // ... implement all other IPage members by delegating to _page
        // (truncated for brevity - would need full implementation)
    }

    public class PatchrightConfig
    {
        public bool EnableCspBypass { get; set; } = true;
        public bool EnableScriptInjection { get; set; } = true;
        public bool EnableNetworkInterception { get; set; } = true;
        public List<string> DisabledFeatures { get; set; } = new();
    }

    // Factory for creating Patchright browsers
    public static class PatchrightFactory
    {
        public static async Task<PatchrightBrowser> CreateAsync(PatchrightConfig? config = null)
        {
            config ??= new PatchrightConfig();
            
            var playwright = await Playwright.CreateAsync();
            var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Args = GetPatchrightArgs(config),
                Headless = false
            });

            return new PatchrightBrowser(browser, config);
        }

        private static string[] GetPatchrightArgs(PatchrightConfig config)
        {
            var args = new List<string>
            {
                "--disable-blink-features=AutomationControlled",
                "--no-first-run",
                "--password-store=basic",
                "--use-mock-keychain"
            };

            // Remove automation-related flags
            var removedFlags = new[]
            {
                "--enable-automation",
                "--disable-popup-blocking", 
                "--disable-component-update",
                "--disable-default-apps",
                "--disable-extensions"
            };

            return args.ToArray();
        }
    }
}
